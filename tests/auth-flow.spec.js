import { test, expect } from '@playwright/test';

// Configure test with longer timeout
test.setTimeout(120000);

test('Complete auth flow: robust signin/signup', async ({ page }) => {
  console.log('🚀 Starting auth flow test...');

  // Enable console logging from the page
  page.on('console', msg => console.log(`PAGE LOG: ${msg.text()}`));
  page.on('pageerror', error => console.log(`PAGE ERROR: ${error.message}`));

  // Navigate to the auth page
  console.log('📍 Navigating to auth page...');
  await page.goto('http://localhost:5173/auth?redirect=%2F', {
    waitUntil: 'networkidle',
    timeout: 30000
  });
  console.log(`✅ Initial navigation complete. URL: ${page.url()}`);

  await page.waitForTimeout(2000);

  // Take initial screenshot
  await page.screenshot({ path: 'debug-01-initial-load.png' });
  console.log('📷 Initial screenshot saved');

  // Check if user is already logged in by checking if we're redirected away from auth
  if (!page.url().includes('/auth')) {
    console.log('🔄 User appears to be logged in, signing out first...');
    // User is already logged in, sign out first
    await signOutIfLoggedIn(page);

    // Navigate back to auth page
    await page.goto('http://localhost:5173/auth', {
      waitUntil: 'networkidle',
      timeout: 30000
    });
    await page.waitForTimeout(2000);
    console.log(`✅ Navigated back to auth page. URL: ${page.url()}`);
  }

  // Try to sign in with existing credentials first
  console.log('🔐 Attempting to sign in with existing credentials...');
  const signInSuccessful = await attemptSignIn(page);

  if (!signInSuccessful) {
    console.log('📝 Sign in failed, attempting signup...');
    // If sign in failed, try signup process
    await performSignUp(page);
  } else {
    console.log('✅ Sign in successful!');
  }

  // Verify we're logged in
  console.log('🔍 Verifying logged in state...');
  await verifyLoggedIn(page);
  console.log('🎉 Auth flow test completed successfully!');
});

async function signOutIfLoggedIn(page) {
  try {
    console.log('🔍 Looking for profile button to sign out...');

    // Take screenshot before attempting signout
    await page.screenshot({ path: 'debug-02-before-signout.png' });

    // Try multiple selectors for the profile button
    const profileSelectors = [
      'button[aria-label="User Menu"]',
      'button[aria-label="User menu"]',
      'button:has-text("Test Admin")',
      'img[alt="User profile"]',
      'button:has(img[alt="User profile"])'
    ];

    let profileButton = null;
    for (const selector of profileSelectors) {
      const element = page.locator(selector);
      const count = await element.count();
      console.log(`🔍 Checking selector "${selector}": found ${count} elements`);
      if (count > 0) {
        profileButton = element.first();
        console.log(`✅ Found profile button with selector: ${selector}`);
        break;
      }
    }

    if (profileButton) {
      await profileButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Profile button clicked, looking for sign out option...');

      // Take screenshot after clicking profile button
      await page.screenshot({ path: 'debug-03-profile-menu-open.png' });

      // Click sign out
      const signOutButton = page.locator('div[role="menuitem"]:has-text("Sign Out")');
      if (await signOutButton.count() > 0) {
        await signOutButton.click();
        await page.waitForTimeout(2000);
        console.log('✅ Successfully signed out');
      } else {
        console.log('⚠️ Sign out button not found in menu');
      }
    } else {
      console.log('⚠️ No profile button found, user may not be logged in');
    }
  } catch (error) {
    console.log(`⚠️ Error during sign out: ${error.message}`);
    // If sign out fails, continue - we'll handle it in the main flow
  }
}

async function attemptSignIn(page) {
  try {
    // Check if we're on the signup page (no admin exists yet)
    const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
    if (await getStartedButton.count() > 0) {
      // No admin exists, need to signup first
      return false;
    }

    // Try to sign in with known credentials
    const emailField = page.locator('#email');
    const passwordField = page.locator('#password');
    const signinButton = page.locator('button:has-text("Sign in")');

    if (await emailField.count() > 0 && await passwordField.count() > 0 && await signinButton.count() > 0) {
      await emailField.fill('<EMAIL>');
      await passwordField.fill('testpass123');
      await signinButton.click();
      await page.waitForTimeout(3000);

      // Check if signin was successful (redirected away from auth page)
      if (!page.url().includes('/auth') || page.url() === 'http://localhost:5173/') {
        return true;
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

async function performSignUp(page) {
  // Click Get Started to access signup form
  const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
  await getStartedButton.click();
  await page.waitForTimeout(2000);

  // Fill out admin signup form
  await page.locator('#name').fill('Test Admin');
  await page.locator('#email').fill('<EMAIL>');
  await page.locator('#password').fill('testpass123');

  // Submit signup form
  await page.locator('button:has-text("Create Admin Account")').click();
  await page.waitForTimeout(3000);

  // Click the "Okay, Let's Go!" button if it appears
  const okayLetsGoButton = page.locator('button:has-text("Okay, Let\'s Go!")');
  if (await okayLetsGoButton.count() > 0) {
    await okayLetsGoButton.click();
    await page.waitForTimeout(2000);
  }
}

async function verifyLoggedIn(page) {
  // Check for signs that we're logged in (not on auth page)
  const loggedIn = !page.url().includes('/auth');
  expect(loggedIn).toBe(true);

  // Additional verification: look for user profile button
  const profileButton = page.locator('button[aria-label="User Menu"]');
  await expect(profileButton).toBeVisible({ timeout: 10000 });
}