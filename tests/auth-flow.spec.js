import { test, expect } from '@playwright/test';

// Configure test with longer timeout
test.setTimeout(120000); // Increase to 2 minutes for debugging

test('Complete auth flow: signup then signin', async ({ page }) => {
  // Enable trace for debugging
  await page.context().tracing.start({ screenshots: true, snapshots: true });
  console.log('📍 Starting complete auth flow test');

  // Navigate to the auth page
  await page.goto('http://localhost:5173/auth?redirect=%2F', {
    waitUntil: 'networkidle',
    timeout: 30000
  });

  // Take initial screenshot to verify page loaded correctly
  await page.screenshot({ path: 'debug-initial-page-load.png' });
  console.log('📷 Captured initial page screenshot');

  await page.waitForTimeout(3000);

  // STEP 1: Sign Up Process
  console.log('📍 Step 1: Admin account signup');

  // Click Get Started to access signup form
  const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
  await getStartedButton.click();
  await page.waitForTimeout(2000);

  // Fill out admin signup form
  await page.locator('#name').fill('Test Admin');
  await page.locator('#email').fill('<EMAIL>');
  await page.locator('#password').fill('testpass123');

  console.log('✅ Signup form filled');

  // Submit signup form
  await page.locator('button:has-text("Create Admin Account")').click();
  await page.waitForTimeout(3000);

  const signupUrl = page.url();
  console.log('✅ Signup completed - URL:', signupUrl);

  // STEP 2: Sign Out (since signup automatically logs user in)
  console.log('📍 Step 2: Signing out');

  // Click the "Okay, Let's Go!" button
  const okayLetsGoButton = page.locator('button', { hasText: 'Okay, Let\'s Go!' });
  await okayLetsGoButton.click();
  await page.waitForTimeout(2000);

  // First, find and click the profile button/icon
  const profileButton = page.locator('button[data-menu-trigger], button[data-melt-dropdown-menu-trigger]');

  // Add debugging to check if profile button exists
  const profileButtonCount = await profileButton.count();
  console.log(`📍 Found ${profileButtonCount} profile buttons`);

  if (profileButtonCount === 0) {
    console.log('⚠️ No profile button found, taking screenshot for debugging');
    await page.screenshot({ path: 'debug-no-profile-button.png' });

    // Try to get page HTML to see what's actually there
    console.log('📍 Current page HTML structure:');
    const bodyHTML = await page.evaluate(() => document.body.innerHTML.substring(0, 1000) + '...');
    console.log(bodyHTML);

    // Try alternative selectors
    console.log('📍 Trying alternative selectors for profile button');
    const altButtons = await page.locator('button').all();
    console.log(`Found ${altButtons.length} total buttons on page`);

    // Try to find any menu-related buttons
    const menuButtons = await page.locator('button[data-*="menu"]').all();
    console.log(`Found ${menuButtons.length} potential menu buttons`);
  }

  console.log('📍 Attempting to click profile button');
  try {
    // Set a shorter timeout for debugging purposes
    await profileButton.click({ timeout: 15000 });
    console.log('✅ Profile button clicked successfully');
  } catch (error) {
    console.error(`❌ Error clicking profile button: ${error.message}`);
    await page.screenshot({ path: 'debug-profile-button-error.png' });

    // Retry logic with force option
    console.log('🔄 Retrying with force option...');
    try {
      await profileButton.click({ force: true, timeout: 10000 });
      console.log('✅ Profile button clicked successfully with force option');
    } catch (retryError) {
      console.error(`❌ Retry also failed: ${retryError.message}`);

      // Last resort: try JavaScript click
      console.log('🔄 Trying JavaScript click as last resort...');
      try {
        await page.evaluate(() => {
          // Try to find and click any menu trigger button
          const menuButtons = document.querySelectorAll('button[data-menu-trigger], button[data-melt-dropdown-menu-trigger]');
          if (menuButtons.length > 0) {
            console.log(`Found ${menuButtons.length} menu buttons via JS`);
            menuButtons[0].click();
            return true;
          }
          return false;
        });
        console.log('✅ JavaScript click attempt completed');
      } catch (jsError) {
        console.error(`❌ JavaScript click also failed: ${jsError.message}`);
        throw error; // Throw original error if all attempts fail
      }
    }
  }

  await page.waitForTimeout(1000);

  // Now that the dropdown is open, find and click the sign out button
  // This specific selector targets the sign out menuitem with text "Sign Out"
  const signOutButton = page.locator('div[role="menuitem"] div.truncate:has-text("Sign Out")').first();
  // Alternative selector focusing on the menu item directly
  if (await signOutButton.count() === 0) {
    const altSignOutButton = page.locator('div[data-melt-dropdown-menu-item], div[data-menu-item]').filter({ hasText: 'Sign Out' });
    if (await altSignOutButton.count() > 0) {
      await altSignOutButton.click();
      await page.waitForTimeout(2000);
      console.log('✅ Signed out successfully');
    } else {
      // If no sign out button found, manually navigate to auth page
      console.log('📍 No sign out button found, navigating to auth page');
    }
  } else {
    await signOutButton.click();
    await page.waitForTimeout(2000);
    console.log('✅ Signed out successfully');
  }

  // STEP 3: Sign In Process
  console.log('📍 Step 3: Admin account signin');

  // Navigate to auth page for signin
  await page.goto('http://localhost:5173/auth', {
    waitUntil: 'networkidle',
    timeout: 30000
  });

  await page.waitForTimeout(2000);

  // Fill in signin form (should now show login form since admin exists)
  const emailField = page.locator('#email');
  const passwordField = page.locator('#password');
  const signinButton = page.locator('button:has-text("Sign in")');

  await emailField.fill('<EMAIL>');
  await passwordField.fill('testpass123');

  console.log('✅ Signin form filled');

  // Submit signin form
  await signinButton.click();
  await page.waitForTimeout(3000);

  // Verify successful signin (should redirect away from auth page)
  const signinUrl = page.url();
  const signinSuccessful = !signinUrl.includes('/auth') || signinUrl === 'http://localhost:5173/';

  console.log('✅ Signin completed:', signinSuccessful, '- URL:', signinUrl);

  // STEP 4: Verify we're logged in
  console.log('📍 Step 4: Verifying logged in state');

  // Check for signs that we're logged in (not on auth page)
  const loggedIn = !page.url().includes('/auth');
  console.log('✅ User successfully logged in:', loggedIn);

  // Stop tracing and save trace file
  await page.context().tracing.stop({ path: 'auth-flow-trace.zip' });
  console.log('📊 Trace file saved for debugging');
});