#!/bin/bash

set -e  # Exit on any error

echo "🔄 Testing signup with detailed logging..."

# Stop any running servers
echo "🛑 Stopping servers..."
./stop-e2e.sh

# Start fresh server with clean database
echo "🚀 Starting fresh server..."
./start-e2e.sh

# Wait longer for server to be fully ready
echo "⏳ Waiting for server to be ready..."
sleep 8

# Run the signup test with more verbose output
echo "🧪 Running detailed signup test..."
npx playwright test tests/signup-form.spec.js --reporter=line --timeout=60000

# Also run the form validation test to see behavior
echo "🧪 Running form validation test..."
npx playwright test tests/signup-validation.spec.js --reporter=line --timeout=60000

# Stop the server
echo "🛑 Stopping server..."
./stop-e2e.sh

echo "✅ Detailed signup tests completed!"
