#!/bin/bash

# Run E2E Tests Script
# This script runs the Playwright E2E test suite for Open WebUI

set -e  # Exit on any error

echo "🎭 Starting E2E tests..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if Playwright browsers are installed
if ! npx playwright --version > /dev/null 2>&1; then
    echo "🌐 Installing Playwright browsers..."
    npx playwright install
fi

# Run tests with basic options
echo "🚀 Running Playwright E2E tests..."
npx playwright test

echo "✅ E2E tests completed!"
