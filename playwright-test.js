const { expect, test } = require('@playwright/test');

test('Homepage shows and activates Get started button', async ({ page }) => {
  await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });

  const getStartedText = page.locator('#get-started');
  await expect(getStartedText).toBeVisible();

  const getStartedBtn = page.locator('button[aria-labelledby="get-started"]');
  await expect(getStartedBtn).toBeVisible();
  await getStartedBtn.click();

  // Optionally assert navigation or UI change after click
});
