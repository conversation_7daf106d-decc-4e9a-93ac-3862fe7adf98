#!/bin/bash

# Open WebUI Backend Stopper
# Stops only the backend service using PID

# Get the project root directory (parent of bash-scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== Stopping Open WebUI Backend ==="

# Stop backend using stored PID (most reliable method)
if [ -f "$PROJECT_ROOT/backend.pid" ]; then
	BACKEND_PID=$(cat "$PROJECT_ROOT/backend.pid")
	if kill -0 "$BACKEND_PID" 2>/dev/null; then
		echo "Stopping backend (PID: $BACKEND_PID)..."
		kill -TERM "$BACKEND_PID" 2>/dev/null || true
		sleep 2
		# Force kill if still running
		if kill -0 "$BACKEND_PID" 2>/dev/null; then
			echo "Force killing backend..."
			kill -KILL "$BACKEND_PID" 2>/dev/null || true
		fi
		echo "• Backend stopped successfully"
	else
		echo "• Backend PID not running (stale PID file)"
	fi
	rm -f "$PROJECT_ROOT/backend.pid"
	echo "• PID file removed"
else
	echo "• No backend.pid file found"
fi

# Fallback cleanup using process patterns
echo "Performing fallback cleanup..."
if pkill -f "uvicorn.*open_webui" 2>/dev/null; then
	echo "• Killed remaining uvicorn processes"
else
	echo "• No remaining uvicorn processes found"
fi

echo ""
echo "=== Backend Stopped Successfully ==="
echo "• Backend process terminated"
echo "• Ready to restart with $SCRIPT_DIR/start-backend.sh"
echo ""
