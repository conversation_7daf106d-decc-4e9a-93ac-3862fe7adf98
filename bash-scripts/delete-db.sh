#!/bin/bash

# Open WebUI SQLite DB Cleaner  
# Deletes the SQLite database file in a robust manner

set -e

# Get the project root directory (parent of bash-scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

DB_PATH="$PROJECT_ROOT/backend/data/webui.db"

echo "=== Open WebUI Database Cleaner ==="

# Check if backend is running and warn user
if [ -f "$PROJECT_ROOT/backend.pid" ]; then
	BACKEND_PID=$(cat "$PROJECT_ROOT/backend.pid")
	if kill -0 "$BACKEND_PID" 2>/dev/null; then
		echo "WARNING: Backend is currently running (PID: $BACKEND_PID)"
		echo "It's recommended to stop the backend first with:"
		echo "  $SCRIPT_DIR/stop-backend.sh"
		echo ""
		read -p "Continue anyway? (y/N): " -n 1 -r
		echo
		if [[ ! $REPLY =~ ^[Yy]$ ]]; then
			echo "Operation cancelled."
			exit 0
		fi
	fi
fi

# Check if the database file exists
if [ -f "$DB_PATH" ]; then
	echo "Found database file: $DB_PATH"
	
	# Get file size for confirmation
	DB_SIZE=$(du -h "$DB_PATH" | cut -f1)
	echo "Database size: $DB_SIZE"
	
	# Confirm deletion
	echo ""
	read -p "Delete the database file? This will remove all data! (y/N): " -n 1 -r
	echo
	if [[ ! $REPLY =~ ^[Yy]$ ]]; then
		echo "Operation cancelled."
		exit 0
	fi
	
	# Delete the file
	echo "Deleting database file..."
	rm -f "$DB_PATH"
	
	# Verify deletion
	if [ ! -f "$DB_PATH" ]; then
		echo "• Database file deleted successfully"
	else
		echo "Error: Failed to delete database file"
		exit 1
	fi
else
	echo "• No database file found at: $DB_PATH"
	echo "• Nothing to delete"
fi

# Also check for any backup files
BACKUP_FILES=$(find "$PROJECT_ROOT/backend/data" -name "*.db-*" 2>/dev/null || true)
if [ -n "$BACKUP_FILES" ]; then
	echo ""
	echo "Found potential backup files:"
	echo "$BACKUP_FILES"
	echo ""
	read -p "Delete backup files too? (y/N): " -n 1 -r
	echo
	if [[ $REPLY =~ ^[Yy]$ ]]; then
		echo "$BACKUP_FILES" | xargs rm -f
		echo "• Backup files deleted"
	fi
fi

echo ""
echo "=== Database Cleanup Complete ==="
echo "• Database files removed"
echo "• Fresh database will be created on next backend startup"
echo "• Start backend with: $SCRIPT_DIR/start-backend.sh"
echo ""
