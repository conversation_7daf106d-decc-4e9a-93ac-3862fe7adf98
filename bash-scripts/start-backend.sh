#!/bin/bash

# Open WebUI Backend Starter
# Starts only the backend service with PID tracking

set -e

# Get the project root directory (parent of bash-scripts)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== Starting Open WebUI Backend ===" 

# Clean up any existing backend processes
echo "Cleaning up existing backend processes..."
pkill -f "uvicorn.*open_webui" 2>/dev/null || true

# Remove old PID file if it exists
[ -f "$PROJECT_ROOT/backend.pid" ] && rm -f "$PROJECT_ROOT/backend.pid"

# Start backend service
echo "Starting backend on port 8080..."
cd "$PROJECT_ROOT/backend"
nohup bash -c "source ../.venv/bin/activate && ./dev.sh" > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid
cd "$PROJECT_ROOT"

# Give backend time to initialize
echo "Waiting for backend to initialize..."
sleep 5

# Verify backend is running
if kill -0 "$BACKEND_PID" 2>/dev/null; then
	echo ""
	echo "=== Backend Started Successfully ==="
	echo "Backend:  http://localhost:8080 (PID: $BACKEND_PID)"
	echo ""
	echo "Commands:"
	echo "  • Stop backend: $SCRIPT_DIR/stop-backend.sh"
	echo "  • Monitor logs: tail -f $PROJECT_ROOT/backend.log"
	echo "  • Check status: kill -0 \$(cat $PROJECT_ROOT/backend.pid)"
	echo ""
else
	echo "Error: Backend failed to start"
	rm -f "$PROJECT_ROOT/backend.pid"
	exit 1
fi
