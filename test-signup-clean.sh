#!/bin/bash

set -e  # Exit on any error

echo "🔄 Testing signup with clean server restart..."

# Stop any running servers
echo "🛑 Stopping servers..."
./stop-e2e.sh

# Start fresh server with clean database
echo "🚀 Starting fresh server..."
./start-e2e.sh

# Wait a bit for server to be fully ready
echo "⏳ Waiting for server to be ready..."
sleep 5

# Run only the signup test
echo "🧪 Running signup test..."
npx playwright test tests/signup-form.spec.js --reporter=line

# Stop the server
echo "🛑 Stopping server..."
./stop-e2e.sh

echo "✅ Signup test completed with clean database!"
