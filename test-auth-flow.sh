#!/bin/bash

set -e  # Exit on any error

echo "🔄 Testing complete auth flow: signup → signin..."

# Stop and start fresh server with clean database
./stop-e2e.sh
./start-e2e.sh

# Wait for server
echo "⏳ Waiting for server..."
sleep 8

# Run the consolidated auth flow test
echo "🧪 Running complete auth flow test..."
npx playwright test tests/auth-flow.spec.js --reporter=line

# Cleanup
./stop-e2e.sh

echo "✅ Complete auth flow test completed!"
