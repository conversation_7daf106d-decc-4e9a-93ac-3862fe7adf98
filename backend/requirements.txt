fastapi==0.115.7
uvicorn[standard]==0.35.0
pydantic==2.11.7
python-multipart==0.0.20

python-socketio==5.13.0
python-jose==3.4.0
passlib[bcrypt]==1.7.4
cryptography

requests==2.32.4
aiohttp==3.11.11
async-timeout
aiocache
aiofiles
starlette-compress==1.6.0
httpx[socks,http2,zstd,cli,brotli]==0.28.1

sqlalchemy==2.0.38
alembic==1.14.0
peewee==3.18.1
peewee-migrate==1.12.2
psycopg2-binary==2.9.9
pgvector==0.4.0
PyMySQL==1.1.1
bcrypt==4.3.0

pymongo
redis
boto3==1.35.53

argon2-cffi==23.1.0
APScheduler==3.10.4

pycrdt==0.12.25

RestrictedPython==8.0

loguru==0.7.3
asgiref==3.8.1

# AI libraries
openai
anthropic
google-genai==1.15.0
google-generativeai==0.8.5
tiktoken

langchain==0.3.26
langchain-community==0.3.26

fake-useragent==2.1.0
chromadb==0.6.3
posthog==5.4.0
pymilvus==2.5.0
qdrant-client==1.14.3
opensearch-py==2.8.0
playwright==1.49.1 # Caution: version must match docker-compose.playwright.yaml
elasticsearch==9.0.1
pinecone==6.0.2

transformers
sentence-transformers==4.1.0
accelerate
colbert-ai==0.2.21
einops==0.8.1


ftfy==6.2.3
pypdf==4.3.1
fpdf2==2.8.2
pymdown-extensions==10.14.2
docx2txt==0.8
python-pptx==1.0.2
unstructured==0.16.17
nltk==3.9.1
Markdown==3.7
pypandoc==1.15
pandas==2.2.3
openpyxl==3.1.5
pyxlsb==1.0.10
xlrd==2.0.1
validators==0.35.0
psutil
sentencepiece
soundfile==0.13.1
azure-ai-documentintelligence==1.0.2

pillow==11.2.1
opencv-python-headless==*********
rapidocr-onnxruntime==1.4.4
rank-bm25==0.2.2

onnxruntime==1.20.1

faster-whisper==1.1.1

PyJWT[crypto]==2.10.1
authlib==1.4.1

black==25.1.0
langfuse==2.44.0
youtube-transcript-api==1.1.0
pytube==15.0.0

pydub
ddgs==9.0.0

## Google Drive
google-api-python-client
google-auth-httplib2
google-auth-oauthlib

## Tests
docker~=7.1.0
pytest~=8.3.5
pytest-docker~=3.1.1

googleapis-common-protos==1.63.2
google-cloud-storage==2.19.0

azure-identity==1.23.0
azure-storage-blob==12.24.1


## LDAP
ldap3==2.9.1

## Firecrawl
firecrawl-py==1.12.0

# Sougou API SDK(Tencentcloud SDK)
tencentcloud-sdk-python==3.0.1336

## Trace
opentelemetry-api==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-exporter-otlp==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-instrumentation-sqlalchemy==0.53b1
opentelemetry-instrumentation-redis==0.53b1
opentelemetry-instrumentation-requests==0.53b1
opentelemetry-instrumentation-logging==0.53b1
opentelemetry-instrumentation-httpx==0.53b1
opentelemetry-instrumentation-aiohttp-client==0.53b1
