#!/bin/bash

# Open WebUI E2E Service Starter
# Starts frontend and backend services with automatic port management

set -e

echo "=== Starting Open WebUI E2E Services ==="

# Clean up any existing services and ports
echo "Cleaning up existing services..."
pkill -f "uvicorn.*open_webui" 2>/dev/null || true
pkill -f "vite.*dev" 2>/dev/null || true
pkill -f "npm.*dev" 2>/dev/null || true

# Force cleanup of frontend ports to ensure port 5173 availability
echo "Ensuring port 5173 availability..."
for port in 5173 5174 5175; do
    lsof -ti:$port | xargs -r kill -9 2>/dev/null || true
done
sleep 2

# Start backend service
echo "Starting backend on port 8080..."
cd backend
nohup bash -c "source ../.venv/bin/activate && ./dev.sh" > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid
cd ..

# Start frontend service  
echo "Starting frontend (will use port 5173)..."
nohup npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > frontend.pid

# Wait for services to initialize
echo "Waiting for services to initialize..."
sleep 15

# Detect and verify frontend port
FRONTEND_PORT=$(grep -o "Local:.*http://localhost:[0-9]*" frontend.log | grep -o "[0-9]*" | head -1)
if [ -z "$FRONTEND_PORT" ]; then
    echo "Error: Could not detect frontend port"
    exit 1
fi

# Display service information
echo ""
echo "=== Services Started Successfully ==="
echo "Frontend: http://localhost:$FRONTEND_PORT (PID: $FRONTEND_PID)"
echo "Backend:  http://localhost:8080 (PID: $BACKEND_PID)"
echo ""
echo "Next steps:"
echo "  • Run tests: npx playwright test"
echo "  • Interactive: npx playwright test --ui" 
echo "  • Stop services: ./stop-e2e.sh"
echo "  • Monitor logs: tail -f frontend.log backend.log"
echo ""
